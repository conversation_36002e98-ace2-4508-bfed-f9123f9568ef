[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "archery.settings"
python_files = "tests.py test_*.py *_tests.py"

[tool.coverage.run]
source = [
    "."
]
omit = [
    # omit anything in a .local directory anywhere
    "src*",
    # omit everything in /usr
    "downloads*",
    # omit this single file
    "sql/migrations/*",
    "venv*"
]

[tool.coverage.report]
omit = [
    # omit anything in a .local directory anywhere
    "src*",
    # omit everything in /usr
    "downloads*",
    # omit this single file
    "sql/migrations/*",
    "venv*"
]